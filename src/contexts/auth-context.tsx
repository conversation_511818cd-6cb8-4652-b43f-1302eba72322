'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Models } from 'appwrite';
import {
  account,
  databases,
  storage,
  ID,
  DATABASE_ID,
  USERS_COLLECTION_ID,
  AVATARS_BUCKET_ID,
  UserProfile,
  UserPreferences,
  DEFAULT_USER_PREFERENCES,
  OAuthProvider,
  OAUTH_PROVIDERS,
  handleAppwriteError,
  AppwriteError
} from '@/lib/appwrite';
import { SessionManager } from '@/lib/auth-utils';

// Authentication state interface
interface AuthState {
  user: Models.User<Models.Preferences> | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

// Authentication actions interface
interface AuthActions {
  // Authentication methods
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => Promise<void>;
  
  // OAuth methods
  loginWithOAuth: (provider: OA<PERSON>Provider, successUrl?: string, failureUrl?: string) => Promise<void>;
  
  // Password management
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (userId: string, secret: string, password: string) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  
  // Email verification
  sendEmailVerification: () => Promise<void>;
  verifyEmail: (userId: string, secret: string) => Promise<void>;
  
  // Profile management
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  uploadAvatar: (file: File) => Promise<string>;
  deleteAvatar: () => Promise<void>;
  
  // Preferences
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>;
  
  // Session management
  refreshSession: () => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  deleteAllSessions: () => Promise<void>;
  
  // Utility methods
  clearError: () => void;
  checkSession: () => Promise<boolean>;
}

// Combined context type
type AuthContextType = AuthState & AuthActions;

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Auth provider props
interface AuthProviderProps {
  children: React.ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  });

  // Helper function to update state
  const updateState = useCallback((updates: Partial<AuthState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Helper function to handle errors
  const handleError = useCallback((error: any) => {
    const appwriteError = handleAppwriteError(error);

    // Provide more user-friendly error messages
    let userMessage = appwriteError.message;

    switch (appwriteError.code) {
      case '401':
        userMessage = 'Invalid email or password. Please check your credentials and try again.';
        break;
      case '409':
        if (appwriteError.message.includes('session')) {
          userMessage = 'A session is already active. Please try logging in again.';
        } else if (appwriteError.message.includes('user')) {
          userMessage = 'An account with this email already exists. Please try logging in instead.';
        }
        break;
      case '429':
        userMessage = 'Too many attempts. Please wait a moment before trying again.';
        break;
      case '500':
        userMessage = 'Server error. Please try again later.';
        break;
      case 'INSUFFICIENT_PERMISSIONS':
        userMessage = 'Unable to create your profile. Please contact support if this persists.';
        break;
      case 'PROFILE_EXISTS':
        userMessage = 'Your profile already exists. Refreshing your session...';
        break;
    }

    updateState({ error: userMessage, isLoading: false });
    console.error('Auth error:', appwriteError);
  }, [updateState]);

  // Clear error
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Fetch user profile from database
  const fetchUserProfile = useCallback(async (userId: string): Promise<UserProfile | null> => {
    try {
      const profile = await databases.getDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId
      );
      return profile as unknown as UserProfile;
    } catch (error: any) {
      if (error.code === 404) {
        // Profile doesn't exist, create one
        return await createUserProfile(userId);
      }
      throw error;
    }
  }, []);

  // Create user profile in database
  const createUserProfile = useCallback(async (userId: string): Promise<UserProfile> => {
    try {
      const user = await account.get();

      // Prepare profile data without document metadata fields
      const profileData = {
        userId,
        name: user.name,
        email: user.email,
        preferences: DEFAULT_USER_PREFERENCES,
        subscription: {
          plan: 'starter',
          status: 'active',
        },
        usage: {
          vmsCreated: 0,
          storageUsed: 0,
          computeHours: 0,
          lastActivity: new Date().toISOString(),
        },
        security: {
          twoFactorEnabled: false,
          lastPasswordChange: new Date().toISOString(),
          loginSessions: 1,
        },
      };

      // Create document with proper permissions
      const createdProfile = await databases.createDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        userId,
        profileData,
        [
          `read("user:${userId}")`,
          `write("user:${userId}")`,
          `delete("user:${userId}")`
        ]
      );

      return createdProfile as unknown as UserProfile;
    } catch (error: any) {
      console.error('Failed to create user profile:', error);

      // Provide more specific error handling
      if (error.code === 401) {
        throw new AppwriteError(
          'Insufficient permissions to create user profile. Please ensure you are properly authenticated.',
          'INSUFFICIENT_PERMISSIONS',
          'authorization_error'
        );
      }

      if (error.code === 409) {
        throw new AppwriteError(
          'User profile already exists.',
          'PROFILE_EXISTS',
          'conflict_error'
        );
      }

      throw handleAppwriteError(error);
    }
  }, []);

  // Check current session
  const checkSession = useCallback(async (): Promise<boolean> => {
    try {
      updateState({ isLoading: true, error: null });
      
      const user = await account.get();
      const profile = await fetchUserProfile(user.$id);
      
      updateState({
        user,
        profile,
        isAuthenticated: true,
        isLoading: false,
      });
      
      return true;
    } catch (error) {
      updateState({
        user: null,
        profile: null,
        isAuthenticated: false,
        isLoading: false,
      });
      return false;
    }
  }, [updateState, fetchUserProfile]);

  // Login with email and password
  const login = useCallback(async (email: string, password: string) => {
    try {
      updateState({ isLoading: true, error: null });

      // Use safer session creation that handles existing sessions
      const sessionResult = await SessionManager.createSessionSafely(email, password);

      if (!sessionResult.success) {
        throw new AppwriteError(sessionResult.error || 'Failed to create session');
      }

      await checkSession();
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError, checkSession]);

  // Register new user
  const register = useCallback(async (email: string, password: string, name: string) => {
    try {
      updateState({ isLoading: true, error: null });

      // Create the user account first
      await account.create(ID.unique(), email, password, name);

      // Use safer session creation that handles existing sessions
      const sessionResult = await SessionManager.createSessionSafely(email, password);

      if (!sessionResult.success) {
        throw new AppwriteError(sessionResult.error || 'Failed to create session after registration');
      }

      await checkSession();
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError, checkSession]);

  // Logout
  const logout = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });
      
      await account.deleteSession('current');
      updateState({
        user: null,
        profile: null,
        isAuthenticated: false,
        isLoading: false,
      });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // OAuth login
  const loginWithOAuth = useCallback(async (
    provider: OAuthProvider, 
    successUrl?: string, 
    failureUrl?: string
  ) => {
    try {
      updateState({ isLoading: true, error: null });
      
      const redirectUrl = successUrl || `${window.location.origin}/auth/callback`;
      const failUrl = failureUrl || `${window.location.origin}/auth/error`;
      
      account.createOAuth2Session(
        OAUTH_PROVIDERS[provider] as any,
        redirectUrl,
        failUrl
      );
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Forgot password
  const forgotPassword = useCallback(async (email: string) => {
    try {
      updateState({ isLoading: true, error: null });
      
      const resetUrl = `${window.location.origin}/auth/reset-password`;
      await account.createRecovery(email, resetUrl);
      
      updateState({ isLoading: false });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Reset password
  const resetPassword = useCallback(async (userId: string, secret: string, password: string) => {
    try {
      updateState({ isLoading: true, error: null });
      
      await account.updateRecovery(userId, secret, password);
      updateState({ isLoading: false });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Update password
  const updatePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    try {
      updateState({ isLoading: true, error: null });
      
      await account.updatePassword(newPassword, currentPassword);
      updateState({ isLoading: false });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Send email verification
  const sendEmailVerification = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });
      
      const verificationUrl = `${window.location.origin}/auth/verify-email`;
      await account.createVerification(verificationUrl);
      
      updateState({ isLoading: false });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Verify email
  const verifyEmail = useCallback(async (userId: string, secret: string) => {
    try {
      updateState({ isLoading: true, error: null });
      
      await account.updateVerification(userId, secret);
      await checkSession(); // Refresh user data
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError, checkSession]);

  // Update profile
  const updateProfile = useCallback(async (updates: Partial<UserProfile>) => {
    try {
      if (!state.user || !state.profile) {
        throw new Error('User not authenticated');
      }

      updateState({ isLoading: true, error: null });

      // Remove document metadata fields from updates
      const { $id, $createdAt, $updatedAt, ...cleanUpdates } = updates;

      const updatedProfile = await databases.updateDocument(
        DATABASE_ID,
        USERS_COLLECTION_ID,
        state.user.$id,
        cleanUpdates
      );

      updateState({
        profile: updatedProfile as unknown as UserProfile,
        isLoading: false,
      });
    } catch (error) {
      handleError(error);
    }
  }, [state.user, state.profile, updateState, handleError]);

  // Upload avatar
  const uploadAvatar = useCallback(async (file: File): Promise<string> => {
    try {
      if (!state.user) {
        throw new Error('User not authenticated');
      }

      updateState({ isLoading: true, error: null });
      
      // Delete existing avatar if it exists
      if (state.profile?.avatar) {
        try {
          await storage.deleteFile(AVATARS_BUCKET_ID, state.profile.avatar);
        } catch (error) {
          // Ignore error if file doesn't exist
        }
      }
      
      // Upload new avatar
      const fileId = ID.unique();
      const uploadedFile = await storage.createFile(AVATARS_BUCKET_ID, fileId, file);
      
      // Update profile with new avatar
      await updateProfile({ avatar: uploadedFile.$id });
      
      updateState({ isLoading: false });
      return uploadedFile.$id;
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [state.user, state.profile, updateState, handleError, updateProfile]);

  // Delete avatar
  const deleteAvatar = useCallback(async () => {
    try {
      if (!state.user || !state.profile?.avatar) {
        return;
      }

      updateState({ isLoading: true, error: null });
      
      await storage.deleteFile(AVATARS_BUCKET_ID, state.profile.avatar);
      await updateProfile({ avatar: undefined });
      
      updateState({ isLoading: false });
    } catch (error) {
      handleError(error);
    }
  }, [state.user, state.profile, updateState, handleError, updateProfile]);

  // Update preferences
  const updatePreferences = useCallback(async (preferences: Partial<UserPreferences>) => {
    try {
      if (!state.profile) {
        throw new Error('User profile not loaded');
      }

      const updatedPreferences = {
        ...state.profile.preferences,
        ...preferences,
      };

      await updateProfile({ preferences: updatedPreferences });
    } catch (error) {
      handleError(error);
    }
  }, [state.profile, updateProfile, handleError]);

  // Refresh session
  const refreshSession = useCallback(async () => {
    await checkSession();
  }, [checkSession]);

  // Delete specific session
  const deleteSession = useCallback(async (sessionId: string) => {
    try {
      updateState({ isLoading: true, error: null });
      
      await account.deleteSession(sessionId);
      updateState({ isLoading: false });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Delete all sessions
  const deleteAllSessions = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });
      
      await account.deleteSessions();
      updateState({
        user: null,
        profile: null,
        isAuthenticated: false,
        isLoading: false,
      });
    } catch (error) {
      handleError(error);
    }
  }, [updateState, handleError]);

  // Check session on mount and cleanup expired sessions
  useEffect(() => {
    const initializeAuth = async () => {
      // Clean up any expired sessions first
      await SessionManager.cleanupSessions();

      // Then check current session
      await checkSession();
    };

    initializeAuth();
  }, [checkSession]);

  // Context value
  const contextValue: AuthContextType = {
    // State
    ...state,
    
    // Actions
    login,
    register,
    logout,
    loginWithOAuth,
    forgotPassword,
    resetPassword,
    updatePassword,
    sendEmailVerification,
    verifyEmail,
    updateProfile,
    uploadAvatar,
    deleteAvatar,
    updatePreferences,
    refreshSession,
    deleteSession,
    deleteAllSessions,
    clearError,
    checkSession,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
